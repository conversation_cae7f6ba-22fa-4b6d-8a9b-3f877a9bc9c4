FROM infiniflow/ragflow:v0.19.0
USER root
WORKDIR /ragflow
ENV PYTHONPATH="/ragflow:${PYTHONPATH}"

# 1) Trust Zscaler CA for system tools
COPY zscaler.crt /usr/local/share/ca-certificates/zscaler.crt
RUN update-ca-certificates
RUN npm config set cafile "/etc/ssl/certs/ca-certificates.crt" \
 && npm config set strict-ssl false

# 2) Build and bundle the web UI
WORKDIR /ragflow/web
COPY docs /ragflow/docs
COPY web/ ./
RUN npm install && npm run build

# 3) Configure HTTP/HTTPS proxies for apt, wget, pip
WORKDIR /ragflow
ARG http_proxy
ARG https_proxy
ARG no_proxy
ENV http_proxy="${http_proxy}"
ENV https_proxy="${https_proxy}"
# Bypass proxy for PyTorch downloads
ENV no_proxy="${no_proxy},download.pytorch.org"
RUN printf 'Acquire::http::Proxy \"%s\";\nAcquire::https::Proxy \"%s\";\n' \
      "${http_proxy}" "${https_proxy}" \
    > /etc/apt/apt.conf.d/01proxy

# 4) Install fonts and system libraries via APT
ENV DEBIAN_FRONTEND=noninteractive
RUN printf 'Acquire::ForceIPv4 \"true\";\n' > /etc/apt/apt.conf.d/99force-ipv4 && \
    apt-get update && \
    apt-get install -y --no-install-recommends \
      wget \
      ca-certificates \
      fontconfig \
      debconf \
      fonts-liberation \
      fonts-noto-cjk \
      libgl1 && \
    rm -rf /var/lib/apt/lists/* && \
    fc-cache -f -v

# 5) Make pip trust Zscaler CA and handle proxies
ENV PIP_CERT=/etc/ssl/certs/ca-certificates.crt \
    REQUESTS_CA_BUNDLE=/etc/ssl/certs/ca-certificates.crt \
    CURL_CA_BUNDLE=/etc/ssl/certs/ca-certificates.crt \
    SSL_CERT_FILE=/etc/ssl/certs/ca-certificates.crt

# 6) Upgrade pip, install PyTorch & torchvision
RUN pip install --upgrade pip && \
    pip install \
      --cert "$PIP_CERT" \
      --trusted-host download.pytorch.org \
      --timeout=600 \
      --retries=2 \
      --index-url https://download.pytorch.org/whl/cpu \
      torch==2.6.0 \
      torchvision==0.21.0  

# 7) Install other DiT dependencies (exclude detectron2)
COPY ../page_layout_detection-v2.0.0 /ragflow/page_layout_detection_v2_0_0
COPY page_layout_detection-v2.0.0/dit_aug/requirements.txt \
     /ragflow/page_layout_detection-v2.0.0/dit_aug/requirements.txt
RUN grep -v '^detectron2' /ragflow/page_layout_detection-v2.0.0/dit_aug/requirements.txt \
    > /tmp/reqs_no_d2.txt && \
    pip install --no-deps -r /tmp/reqs_no_d2.txt



# 9) Copy custom model and integration code
COPY deepdoc/vision/ /ragflow/deepdoc/vision/
COPY deepdoc/parser/ppt_parser.py /ragflow/deepdoc/parser/ppt_parser.py
COPY deepdoc/vision/layout_recognizer.py /ragflow/deepdoc/vision/layout_recognizer.py
COPY rag/app/presentation.py    /ragflow/rag/app/presentation.py
COPY intergrations/extension_chrome/assets /ragflow/intergrations/extension_chrome/assets

# 10) Copy API files
COPY api/apps/sdk/doc.py /ragflow/api/apps/sdk/doc.py
COPY api/db/services/task_service.py /ragflow/api/db/services/task_service.py
COPY api/utils/api_utils.py /ragflow/api/utils/api_utils.py
COPY api/apps/api_app.py /ragflow/api/apps/api_app.py
COPY api/apps/document_app.py /ragflow/api/apps/document_app.py
COPY api/db/services/file_service.py /ragflow/api/db/services/file_service.py

# 11) Copy core parsing files
COPY deepdoc/vision/__init__.py /ragflow/deepdoc/vision/__init__.py
COPY deepdoc/parser/pdf_parser.py /ragflow/deepdoc/parser/pdf_parser.py
COPY rag/app/naive.py /ragflow/rag/app/naive.py

# 12) Copy user app and email utils
COPY api/apps/user_app.py /ragflow/api/apps/user_app.py
COPY api/utils/email_utils.py /ragflow/api/utils/email_utils.py 