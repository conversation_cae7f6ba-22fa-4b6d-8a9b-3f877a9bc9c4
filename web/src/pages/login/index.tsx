import SvgIcon from '@/components/svg-icon';
import { useAuth } from '@/hooks/auth-hooks';
import {
  useLogin,
  useLoginChannels,
  useLoginWithChannel,
  useRegister,
} from '@/hooks/login-hooks';
import { useSystemConfig } from '@/hooks/system-hooks';
import { rsaPsw } from '@/utils';
import { Button, Checkbox, Form, Input } from 'antd';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'umi';
import RightPanel from './right-panel';

import styles from './index.less';

const Login = () => {
  const [title, setTitle] = useState('login');
  const navigate = useNavigate();
  const { login, loading: signLoading } = useLogin();
  const { register, loading: registerLoading } = useRegister();
  const { channels, loading: channelsLoading } = useLoginChannels();
  const { login: loginWithChannel, loading: loginWithChannelLoading } =
    useLoginWithChannel();
  const { t } = useTranslation('translation', { keyPrefix: 'login' });
  const loading =
    signLoading ||
    registerLoading ||
    channelsLoading ||
    loginWithChannelLoading;
  const { config } = useSystemConfig();
  const registerEnabled = config?.registerEnabled !== 0;

  const { isLogin } = useAuth();
  useEffect(() => {
    if (isLogin) {
      navigate('/knowledge');
    }
  }, [isLogin, navigate]);

  const handleLoginWithChannel = async (channel: string) => {
    await loginWithChannel(channel);
  };

  const changeTitle = () => {
    if (title === 'login' && !registerEnabled) {
      return;
    }
    // Toggle between login and request access modes
    setTitle((title) => (title === 'login' ? 'request_access' : 'login'));

    // Original redirect approach (commented out)
    // if (title === 'login') {
    //   navigate('/login-next?step=1'); // step=1 is SignUp step
    // } else {
    //   setTitle('login');
    // }

    // Original toggle functionality (commented out)
    // setTitle((title) => (title === 'login' ? 'register' : 'login'));
  };
  const [form] = Form.useForm();

  useEffect(() => {
    form.validateFields(['nickname']);
  }, [form]);

  const onCheck = async () => {
    try {
      const params = await form.validateFields();
      // Handle request access - validate company email first
      console.log('Submitting access request for:', params.email);
      const email = params.email.toLowerCase();
      const companyDomains = ['cgg.com', 'viridiengroup.com']; // Add your company domains
      const isCompanyEmail = companyDomains.some(domain => email.endsWith(`@${domain}`));
      if (title === 'request_access' && isCompanyEmail) {
        // Make API call to request access
        const response = await fetch('/v1/user/request_access', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ email: params.email }),
        });
        const result = await response.json();
        if (result.code === 0) {
          alert('Access request submitted successfully. We\'ll get back to you shortly.');
          setTitle('login');
        } else {
          alert(result.message || 'Failed to submit access request. Please try again.');
        }
      } else if (title === 'request_access') {
        alert('Please use your company email address to request access.');
      } else {
        // Normal login flow
        const rsaPassWord = rsaPsw(params.password) as string;
        // Handle login
        const code = await login({
          email: `${params.email}`.trim(),
          password: rsaPassWord,
        });
        if (code === 0) {
          navigate('/knowledge');
        }
      }
    } catch (error) {
      console.error('Error submitting access request:', error);
      alert('Access request submitted. We\'ll get back to you shortly.');
    }
  };
  const formItemLayout = {
    labelCol: { span: 6 },
    // wrapperCol: { span: 8 },
  };

  return (
    <div className={styles.loginPage}>
      <div className={styles.loginLeft}>
        <div className={styles.leftContainer}>
          <div className={styles.loginTitle}>
            <div>{title === 'login' ? t('login') : 'Request Access'}</div>
            <span>{title === 'login' ? t('loginDescription') : 'Please use your company email. We\'ll get back to you shortly.'}</span>
            {/* Original dynamic title (commented out) */}
            {/* <div>{title === 'login' ? t('login') : t('register')}</div> */}
            {/* <span>{title === 'login' ? t('loginDescription') : t('registerDescription')}</span> */}
          </div>

          <Form
            form={form}
            layout="vertical"
            name="dynamic_rule"
            style={{ maxWidth: 600 }}
          >
            <Form.Item
              {...formItemLayout}
              name="email"
              label={t('emailLabel')}
              rules={[{ required: true, message: t('emailPlaceholder') }]}
            >
              <Input size="large" placeholder={t('emailPlaceholder')} />
            </Form.Item>
            {/* Original nickname field for registration (commented out) */}
            {/* {title === 'register' && (
              <Form.Item
                {...formItemLayout}
                name="nickname"
                label={t('nicknameLabel')}
                rules={[{ required: true, message: t('nicknamePlaceholder') }]}
              >
                <Input size="large" placeholder={t('nicknamePlaceholder')} />
              </Form.Item>
            )} */}
            {title === 'login' && (
              <Form.Item
                {...formItemLayout}
                name="password"
                label={t('passwordLabel')}
                rules={[{ required: true, message: t('passwordPlaceholder') }]}
              >
                <Input.Password
                  size="large"
                  placeholder={t('passwordPlaceholder')}
                  onPressEnter={onCheck}
                />
              </Form.Item>
            )}
            {title === 'login' && (
              <Form.Item name="remember" valuePropName="checked">
                <Checkbox> {t('rememberMe')}</Checkbox>
              </Form.Item>
            )}
            <div>
              {registerEnabled && (
                <div>
                  {title === 'login' ? t('signInTip') : 'Already have an account?'}
                  <Button type="link" onClick={changeTitle}>
                    {title === 'login' ? 'Request Access' : t('login')}
                  </Button>
                </div>
              )}
              {/* Original sign up/register toggle (commented out) */}
              {/* {title === 'login' && registerEnabled && (
                <div>
                  {t('signInTip')}
                  <Button type="link" onClick={changeTitle}>
                    {t('signUp')}
                  </Button>
                </div>
              )}
              {title === 'register' && (
                <div>
                  {t('signUpTip')}
                  <Button type="link" onClick={changeTitle}>
                    {t('login')}
                  </Button>
                </div>
              )} */}
            </div>
            <Button
              type="primary"
              block
              size="large"
              onClick={onCheck}
              loading={loading}
            >
              {title === 'login' ? t('login') : 'Request Access'}
              {/* Original dynamic button text (commented out) */}
              {/* {title === 'login' ? t('login') : t('continue')} */}
            </Button>
            {channels && channels.length > 0 && (
              <div className={styles.thirdPartyLoginButton}>
                {channels.map((item) => (
                  <Button
                    key={item.channel}
                    block
                    size="large"
                    onClick={() => handleLoginWithChannel(item.channel)}
                    style={{ marginTop: 10 }}
                  >
                    <div className="flex items-center">
                      <SvgIcon
                        name={item.icon || 'sso'}
                        width={20}
                        height={20}
                        style={{ marginRight: 5 }}
                      />
                      Sign in with {item.display_name}
                    </div>
                  </Button>
                ))}
              </div>
            )}
          </Form>
        </div>
      </div>
      <div className={styles.loginRight}>
        <RightPanel></RightPanel>
      </div>
    </div>
  );
};

export default Login;
