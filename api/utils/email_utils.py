"""
Access request utility functions for Viridoc
"""
import logging


def send_access_request_notification(user_email: str):
    """
    Save access request to CSV file for tracking.

    Args:
        user_email (str): Email of the user requesting access

    Returns:
        bool: True if request saved successfully, False otherwise
    """
    try:
        import csv
        import datetime
        import os

        timestamp = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # Save CSV in the project root directory
        csv_file = "/ragflow/viridoc_access_requests.csv"

        # Check if CSV file exists, if not create it with headers
        logging.debug(f"CWD: {os.getcwd()}")
        logging.debug(f"CSV path: {csv_file}")
        file_exists = os.path.exists(csv_file)
        logging.debug(f"File exists: {file_exists}")

        # Get current user count
        user_num = 1
        if file_exists:
            try:
                with open(csv_file, 'r', newline='', encoding='utf-8') as f:
                    reader = csv.reader(f)
                    rows = list(reader)
                    if len(rows) > 1:  # More than just header
                        user_num = len(rows)  # Header + data rows = next user number
            except:
                user_num = 1

        # Write to CSV file
        with open(csv_file, 'a', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)

            # Write header if file is new
            if not file_exists:
                writer.writerow(['User_Num', 'Timestamp', 'User_Email', 'Status'])

            # Write the new access request
            writer.writerow([user_num, timestamp, user_email, 'PENDING'])

        # Log the request
        logging.debug("=" * 50)
        logging.debug("🚨 NEW VIRIDOC ACCESS REQUEST 🚨")
        logging.debug(f"User #{user_num}: {user_email}")
        logging.debug(f"Request Time: {timestamp}")
        logging.debug(f"CSV Updated: {csv_file}")
        logging.debug("=" * 50)

        return True

    except Exception as e:
        logging.error(f"Failed to save access request to CSV: {str(e)}")
        return False
        
    except Exception as e:
        logging.error(f"Failed to send access request notification: {str(e)}")
        return False



